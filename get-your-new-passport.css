.get-your-new-passport {
    height: 744px;
    padding: 0 60px;
    position: relative;
    background-color:white ;
}
.get-your-new-passport-main {
    padding: 72px 90px;
    background-color: rgba(0, 121, 255, 0.06);
    display: flex;
    flex-direction: column;
    gap: 48px;
    border-radius: 20px;
}
.get-your-new-passport-main .heading {
    height: 116px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.get-your-new-passport-main .heading h1 {
    font-family: 'Inter', sans-serif;
  font-weight: 700;        /* Bold weight */
  font-style: normal;      /* 'Bold' is invalid for font-style */
  font-size: 48px;
  line-height: 58px;
  letter-spacing: 0;
  color: rgba(11, 65, 125, 1);
}
.get-your-new-passport-cards {
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.05);
    background-color: white;
    border-radius: 12px;
    width: 350px;
    height: 435px;
    padding: 24px;
    gap: 14px;
    justify-content: space-between;
}
.get-your-new-passport-main .get-your-new-passport-cards-card-group {
    gap: 44px;
    display: flex;
}
.get-your-new-passport-main .get-your-new-passport-cards h1 {
    font-size: 18px;
    line-height: 22px;
    color: rgba(0, 0, 0, 1);
    font-weight: 500;
}
.get-your-new-passport-main .get-your-new-passport-cards p {
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    line-height: 17px;
    font-weight: 400;
}
.get-your-new-passport-main .get-your-new-passport-cards span {
    background: rgba(0, 149, 255, 0.15);
    color: rgba(0, 149, 255, 1);
    width: fit-content;
    padding: 6px 20px;
    height: 28px;
    line-height: 16px;
    font-size: 10px;
    /* vertical-align: middle; */
    border-radius: 100px;
    font-size: 13px;
    letter-spacing: 0;
    font-weight: 500;
}
.get-your-new-passport-ellipse {
   position: absolute;
    right: 0;
    top: 260px;
  /* width: 100%; */
  /* height: 100%; */
  background: url('./public/images/what-people-are-saying-ellipse.svg') center/cover no-repeat;
   background-image: url('');
    background-position: right;
    background-repeat: no-repeat;
    background-size: contain;
    z-index: -1;
}