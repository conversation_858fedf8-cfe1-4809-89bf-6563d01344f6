.our-passport-vissa {
    padding: 0 60px;
    /* height: 790px; */
}
.our-passport-vissa-main {
    padding: 72px 90px;
    background-color: rgba(255, 194, 0, 0.07);
    border-radius:20px;
    height: 790px;
}
.our-passport-vissa-main-heading {
  height: 108px;
  margin-bottom: 58px;
  gap: 24px;
}
.our-passport-vissa-main-heading h1 {
  font-family: 'Inter',sans-serif;
  margin-bottom: 0;
}
.our-passport-vissa-main-heading p {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-style: normal; /* "Regular" = normal */
  font-size: 14px;
  line-height: 17px; /* 100% = 1 */
  letter-spacing: 0;
  text-align: center;
  color: rgba(11, 65, 125, 1);
  margin-bottom: 0;
}
/* .heading {

} */

.text-carousel-container {
  background: #fff;
  padding: 30px 40px;
  border-radius: 14px;
  /* box-shadow: 0 5px 15px rgba(0,0,0,0.05); */
  max-width: 700px;
  margin:12px 0;
  position: relative;
  width: 70%;
  min-width: 70%;
}
.get-started-your-app-text {
  color: rgba(11, 65, 125, 1);
  font-family: 'Inter', sans-serif;
  font-weight: 600; /* Semi Bold */
  font-style: normal; /* "Semi Bold" is a weight, not a style */
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0;
  padding-left: 0;
  
  text-decoration: underline;
  text-decoration-style: solid;
  text-decoration-thickness: 1px;
  text-underline-offset: 3px;
  text-decoration-skip-ink: auto;
  border: none;
  outline: none;
  background-color: transparent;
}
.our-passport-vissa-content {
    height: 245px;
    box-shadow: 0px 4px 50px 0px rgba(0, 0, 0, 0.03);
    background-color: white;
    border-radius: 20px;
    margin-bottom: 32px;
}
.text-carousel-container-dashes {
  display: flex;
  justify-content: space-between;
}
.our-passport-vissa-content .cards {
    padding: 20px;
    margin: auto 0;
    width: 30%;

}
.our-passport-vissa-content .cards .card-content {
    padding: 20px;
    border: 0.3px solid rgba(11, 65, 125, 0.6);
    border-radius: 18px;
}
.our-passport-vissa-content .cards .card-content .card-content-header-text {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
}
.card-content-data-sub-heading {
  font-size: 20px;
  line-height: 24px;
  font-weight: 400;
}
  .our-passport-vissa-content .cards .card-content .card-content-data {
    /* padding-left: 24px; */
  }
.our-passport-vissa-content .cards .card-content .circle {
    width: 32px;
    height: 32px;
    background-color: rgba(0, 149, 255, 0.1);
}
.our-passport-vissa-content .cards .card-content .circle-bulb {
   width: 32px;
    height: 32px;
  background-color: rgba(48, 223, 0, 0.1);
}

.our-passport-vissa-content .cards .card-content .circle-easy{
  width: 32px;
  height: 32px;
  background-color: rgba(255, 174, 0, 0.1);

}

.text-content {
  min-height: 120px;
  margin-bottom: 20px;
  padding-right: 139px;
}

.carousel-heading {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-style: normal; /* "Medium" is a weight, not a style */
  font-size: 20px;
  line-height: 1; /* 100% = 1 */
  letter-spacing: 0;
  color: rgba(11, 65, 125, 1);
  height: auto;

}

.carousel-description {
  font-family: 'Inter', sans-serif;
  font-weight: 400; /* Regular */
  font-style: normal;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0;
  margin: 0;
  color: rgba(0, 0, 0, 0.8);


}

.carousel-link {
  display: inline-block;
  margin-top: 12px;
  color: #003366;
  font-weight: 500;
  text-decoration: underline;
}
.visa-options-cut-bg-header-div .large {
    display: none;
  }

.carousel-dashes {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
}

.our-passport-dash {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background-color: rgba(210, 210, 210, 0.6);
  transition: width 0.4s ease, background-color 0.4s ease;
}

.our-passport-dash.active {
  width: 30px;
  background-color: rgba(11, 65, 125, 1);
}

@media (max-width: 767px) {
  .our-passport-dash {
  width: 6px;
  height: 6px;
  border-radius: 6px;
}
.our-passport-dash.active {
  width: 18px;
}
.visa-options-cut-bg-header-div .large {
    display: none;
  }
  .our-passport-vissa-content .cards .card-content .card-content-header-text {
  font-size: 18px;
  line-height: 22px;
}
.card-content-data-sub-heading {
  font-size: 16px;
  line-height: 19px;
  font-weight: 400;
}
  .our-passport-vissa {
    padding: 0 20px;
  }
  .our-passport-vissa-main {
    padding: 46px 10px;
    height: auto;
  }
  .our-passport-vissa-main-heading h1 {
    font-size: 24px;
    line-height: 29px;
    height: auto;
    text-align: center;
  }
  .our-passport-vissa-main-heading {
    gap: 20px;
    margin-bottom: 32px;
    text-align: center;
    padding: 0 20px;
    height: auto;
  }
  .our-passport-vissa-content {
    flex-direction: column;
    height: auto;
    margin-bottom: 20px;
  }
  .text-carousel-container-dashes {
    flex-direction: column;
  }
  .text-carousel-container {
    width: 100%;
    padding: 14px 14px 0;
    margin: 0;
  }
  .text-content {
    margin-bottom: 24px;
    padding-right: 0;
    min-height: min-content;
  }
  .text-carousel-container-dashes {
    /* justify-content: flex-start; */
    /* align-self: start; */
  }
  .get-started-your-app-text {
    align-self: flex-start;
    margin-bottom: 24px;
  }
  .our-passport-vissa-content .cards {
    width: 100%;
    padding: 14px;
  }
  .our-passport-vissa-content .cards .card-content {
    padding: 14px;
  }
  .our-passport-vissa-content .cards .card-content .card-content-data {
    padding-left: 4px;
  }
  .our-passport-vissa-content .cards .card-content .circle {
    width: 28px;
    height: 28px;
    background-color: rgba(0, 149, 255, 0.1);
}
.our-passport-vissa-content .cards .card-content .circle-bulb {
   width: 28px;
    height: 28px;
  background-color: rgba(48, 223, 0, 0.1);
}

.our-passport-vissa-content .cards .card-content .circle-easy{
  width: 28px;
  height: 28px;
  background-color: rgba(255, 174, 0, 0.1);

}
}

@media (min-width: 768px) and (max-width:992px) {
    .our-passport-vissa-content {
      flex-direction: column;
      height: auto;
      margin-bottom: 20px;
    }
    .our-passport-vissa-main {
      height: auto;
      padding: 72px 40px;
    }
    .visa-options-cut-bg-header-div .large {
    display: none;
  }
    .visa-option-content {
      gap: 42px;
    }
    .our-passport-vissa-content .cards {
      width: 100%;
      padding: 14px;
    }
    .text-carousel-container-dashes {
    flex-direction: column;
  }
  .get-started-your-app-text {
    align-self: flex-start;
    margin-bottom: 24px;
  }
  .our-passport-vissa-main-heading h1 {
    height: auto;
    font-size: 34px;
    line-height: 44px;
  }
  .text-carousel-container {
    width: 100%;
    padding: 14px 14px 0;
    margin: 0;
  }
  .our-passport-vissa-content .cards .card-content .circle {
    width: 28px;
    height: 28px;
    background-color: rgba(0, 149, 255, 0.1);
}
.our-passport-vissa-content .cards .card-content .circle-bulb {
   width: 28px;
    height: 28px;
  background-color: rgba(48, 223, 0, 0.1);
}

.our-passport-vissa-content .cards .card-content .circle-easy{
  width: 28px;
  height: 28px;
  background-color: rgba(255, 174, 0, 0.1);

}
}

@media (min-width: 993px) and (max-width: 1250px) {
  .text-carousel-container {
    width: 65%;
    min-width: 65%;
  }
  .our-passport-vissa-main {
    height: auto;
    padding: 72px 60px;
  }
  .our-passport-vissa-content .cards {
    width: 35%;
  }
  .card-content-data-sub-heading {
  font-size: 16px;
  line-height: 19px;
  font-weight: 400;
}
.visa-options-cut-bg-header-div .large {
    display: none;
  }
.our-passport-vissa-content .cards .card-content .circle {
    width: 28px;
    height: 28px;
    background-color: rgba(0, 149, 255, 0.1);
}
.our-passport-vissa-content .cards .card-content .circle img {
  width: 15px;
  height: 15px;
}
.our-passport-vissa-content .cards .card-content .circle-bulb img {
  width: 15px;
  height: 15px; 
}
.our-passport-vissa-content .cards .card-content .circle-easy img { 
    width: 15px;
  height: 15px; 
}
.our-passport-vissa-content .cards .card-content .circle-bulb {
   width: 28px;
    height: 28px;
  background-color: rgba(48, 223, 0, 0.1);
}

.our-passport-vissa-content .cards .card-content .circle-easy{
  width: 28px;
  height: 28px;
  background-color: rgba(255, 174, 0, 0.1);

}
}

@media (min-width: 1441px) {
  .our-passport-vissa {
    padding: 0 62px;
  }
  .our-passport-vissa-main {
    padding: 72px 88px;
  }
  .our-passport-vissa-main-heading p {
    font-size: 20px;
    line-height: 24px;
  }
  .our-passport-vissa-main-heading {
    margin-bottom: 43px;
  }
  .text-carousel-container {
    padding: 56px 80px 56px 56px;
    /* height: auto; */
  }
  .carousel-heading {
    font-size: 24px;
    line-height: 29px;
    font-weight: 500;
  }
  .carousel-description {
    font-size: 16px;
    line-height: 21px;
  }
  .text-content{
    margin-bottom: 28px;
    min-height: 80px;
  }
  .our-passport-dash {
    width: 13px;
    height: 13px;
  }
  .our-passport-dash.active {
    width: 39px;
  }
  .visa-options {
    max-width: 1620px;
  }
  .visa-options-top-div {
    width: 281px;
    height: 57px;
  }
  .visa-country-heading {
    font-size: 24px;
    line-height: 29px;
  }
  .visa-country .subheading {
    font-size: 16px;
    line-height: 19px;
  }
  .visa-option-content {
    gap: 150px;
  }
  .visa-country:not(:last-child)::after {
    right: -75px;
  }
  .visa-country {
    width: 277px;
  }
  .visa-options-cut-bg-header-div .large {
    display: block;
    width: 315px;
    height: 91px;
  }
  .visa-options-cut-bg-header-div .medium {
    display: none;
  }
  .our-passport-vissa-content .cards {
    width: 341px;
  }

  .our-passport-vissa-content {
    justify-content: space-between;
  }
  .text-carousel-container {
    min-width: 78%;
  }
}

@media (min-width:1441px) and (max-width:1590px) {
  .text-carousel-container {
    min-width: 70%;
  }
}

