<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passport and Vissa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="./index.css">
    <link rel="stylesheet" href="./styles.css">
    <link rel="stylesheet" href="./four-steps.css">
    <link rel="stylesheet" href="./expedited-passports.css">
    <link rel="stylesheet" href="./what-people-are.css">
    <link rel="stylesheet" href="./our-passport-vissa.css">
    <link rel="stylesheet" href="./frequently-asked-questions.css">
    <link rel="stylesheet" href="./see-our-blogs.css">
    <link rel="stylesheet" href="./footer-section.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link href='https://fonts.googleapis.com/css?family=Inter' rel='stylesheet'>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
</head>
<body>
    <div class="header-class-top-sec">
        <div class="row no-gutters align-items-center align-items-md-baseline align-items-xl-center container-fluid pl-0 justify-content-center text-white fw-medium header-class">
            <div class="col-12 col-md-0 col-lg-0 col-xl-4 col-xxl-3 text-white p-0"></div>
            <div class="col-12 col-md-6 col-lg-6 col-xl-4 col-xxl-4 p-0">
                <p class="mb-0 text-end d-flex justify-content-end gap-2" style="font-family: 'Inter', sans-serif;">
                    <img width="18" height="18" class="bell-icon-class" src="./public/icons/bell.svg" alt="" srcset="">
                    Get Your Passport in Just 24 Hours – Now Available!
                </p>
            </div>
            <div class="col-12 col-md-6 col-lg-6 col-xl-4 col-xxl-5 p-0 header-top-sec-phone-number">
                <div class="d-flex gap-4 justify-content-end">
                    <div class="d-flex justify-content-center gap-2">
                        <img class="header-class-phone-icon" src="./public/phone.svg" alt="" width="16" height="16">
                        <span class="font-inter font-medium" style="font-weight: 500;">Phone: (************)</span>
                    </div>
                    <div class="d-flex justify-content-center gap-2 header-top-sec-phone-number-track-my-app">
                        <img  class="header-class-location-icon"  src="./public/mdi_location.svg" alt="">
                        <span class="font-inter" style="font-weight: 500;">Track My Application</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid p-0">
        <nav class="navbar navbar-light">
            <div class="container-fluid p-0 nav-bar-content">
                <img 
                    style="cursor: pointer;" 
                    onclick="location.href='index.html'"
                    src="./public/logo.svg"
                    class="header-logo-class-attached"
                >
                <button class="mobile-contact-number-header">
                   <img width="16" height="16" src="./public/phone-mobile.svg" alt="" srcset=""> 888-888-111
                </button>
                <div class="nav-btns">
                    <div class="pre-custom-dropdown">
                        <button  class="pre-custom-btn pre-custom-dropdown-toggle pre-custom-dropdown-button" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Passports
                                <img width="10" height="10" src="./public/icons/arrow-down-black.svg" alt="" srcset="">
                            </button>
                        </button>
                        <ul class="pre-custom-dropdown-menu dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li><a href="passport-page-page-design.html" class="pre-custom-dropdown-item dropdown-item" href="#">Passport</a></li>
                            <li><a href="get-your-visa.html" class="pre-custom-dropdown-item dropdown-item" href="#">Another action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Something else</a></li>
                        </ul>
                    </div>
                    <div class="pre-custom-dropdown">
                        <button class="pre-custom-btn pre-custom-dropdown-toggle pre-custom-dropdown-button" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Travel visas
                                <img width="10" height="10" src="./public/icons/arrow-down-black.svg" alt="" srcset="">
                            </button>
                        </button>
                        <ul class="pre-custom-dropdown-menu dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Another action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Something else</a></li>
                        </ul>
                    </div>
                    <div class="pre-custom-dropdown">
                        <button class="pre-custom-btn pre-custom-dropdown-toggle pre-custom-dropdown-button" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            About
                                <img width="10" height="10" src="./public/icons/arrow-down-black.svg" alt="" srcset="">
                            </button>
                        </button>
                        <ul class="pre-custom-dropdown-menu dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Another action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Something else</a></li>
                        </ul>
                    </div>
                    <div class="pre-custom-dropdown">
                        <button class="pre-custom-btn pre-custom-dropdown-toggle pre-custom-dropdown-button" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Resourses
                                <img width="10" height="10" src="./public/icons/arrow-down-black.svg" alt="" srcset="">
                            </button>
                        </button>
                        <ul class="pre-custom-dropdown-menu dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Another action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Something else</a></li>
                        </ul>
                    </div>
                    <button class="pre-custom-btn pre-custom-dropdown-button">
                        Contact
                    </button>
                    <button class="btn login-btn" style="margin-left: 20px;" type="submit">Log in</button>
                </div>
            </div> 
        </nav>
        <section class="expedited-passports-section">
            <div class="main-div-sec">
                <div class="expedited-passports d-flex flex-column gap-20">
                    <div class="expedited-passports-section-info-tag">

                        <div class="info-tag border rounded-pill small primary-blue">
                            <span style="font-weight: 600;letter-spacing: 0px;">From $199+</span> Government fee
                        </div>
                        <div class="info-tag  border rounded-pill small primary-blue">
                            <span style="font-weight: 600;letter-spacing: 0px;">3–4 Business Days</span> with Fast Delivery
                        </div>
                    </div>
                    <p class="expedited-main-heading mb-0 tt-commons" style="color: #0B417D;">Expedited Passports  Made Simple <span class="primary-orange">Guaranteed</span></p>
                    <p class="mb-0 fs-14 lh-100 expedited-main-paragraph" style="color: rgba(0, 0, 0, 0.6); font-family: 'Inter', sans-serif;">
                        We simplify the passport process so you don’t have to worry. No forms confusion, 
                        no errors. Just answer a 
                        few questions and you’re done — in  under 5 minutes. Fast. Trusted. Guaranteed.
                    </p>
                    <p class="mb-0 fs-14 lh-100 expedited-main-paragraph-mobile" style="color: rgba(0, 0, 0, 0.6); font-family: 'Inter', sans-serif;">
                        We simplify the passport process so you don’t have <br/> to worry. No forms confusion, 
                        no errors. Just <br/> answer a 
                        few questions and you’re done — in under <br class="d-none d-lg-block"/> 5 minutes. Fast. Trusted. Guaranteed.
                    </p>
                    <button class="get-started-your-app fw-medium rounded-pill d-inline-flex align-items-center" style="min-width: min-content;">
                        Get started Your Application 
                        <img class="get-started-your-app-img" src="./public/expedited/rounded-arrow.svg" alt="" srcset="">
                    </button>
                    <!-- <div class="high-approval-rate text-black d-flex gap-6 fs-12 lh-100">
                        <img width="12" height="12" src="./public/icons/circle-tick.svg" alt="" srcset="">
                        High approval rate on the first try
                    </div> -->
                    <div class="high-approval-rate-wrapper">
                        <div class="high-approval-rate text-black d-flex gap-6 fs-12 lh-100 active">
                            <img width="12" height="12" src="./public/icons/circle-tick.svg" alt="">
                            High approval rate on the first try
                        </div>
                        <div class="high-approval-rate text-black d-flex gap-6 fs-12 lh-100">
                            <img width="12" height="12" src="./public/icons/circle-tick.svg" alt="">
                            Easy online application process
                        </div>
                        <div class="high-approval-rate text-black d-flex gap-6 fs-12 lh-100">
                            <img width="12" height="12" src="./public/icons/circle-tick.svg" alt="">
                            Fast and secure delivery
                        </div>
                    </div>
                    <div class="d-flex align-items-center fw-medium fs-12 text-black reviews">
                        <span style="font-family: 'Inter', sans-serif;" class="lh-100">11,484 reviews</span>
                        <img class="star-group-passport-sec" width="80" height="14" src="./public/icons/stars-group.svg" alt="">
                        <img class="trust-pilot-passport-sec" width="70" height="17" src="./public/icons/trust-pilot.svg" alt="" srcset="">
                    </div>
                </div>
                
                <div class="expedited-passports-img">
                    <div class="start-your-app-img">
                        <div class="start-your-app-img-position">
                            <div class="start-your-app-img-header">
                                <h6 class="mb-0">Start Your Application Today!</h6>
                            </div>
                            <div class="start-your-app-img-content">
                                <div class="start-your-app-img-content-data">
                                    <img src="./public/icons/circle-checked-filled.svg" alt="">
                                    Apply in minutes
                                </div>
                                <div style="font-weight: 600;" class="start-your-app-img-content-data">
                                    <img src="./public/icons/ellipse-circles-orange.svg" alt="">
                                    Skip the Stress
                                </div>
                                <div class="start-your-app-img-content-data">
                                    <img src="./public/icons/ellipse-circle-gray.svg" alt="">
                                    Your Passport
                                </div>
                                <div class="start-your-app-img-content-data">
                                    <img src="./public/icons/ellipse-circle-gray.svg" alt="">
                                    Done Right Guaranteed.
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <img class="start-your-app-img" src="./public/expedited/start-your-application.svg"  alt="" srcset=""> -->
                    <div class="passport-delivered d-flex">
                        <img width="26" height="26" src="./public/icons/navigation.svg" alt="no" width="26" height="26" srcset="">
                        Passport Delivered
                    </div>
                </div>
                <img class="left-side-plane-icon" src="./public/icons/plane.svg" alt="">
            </div>
            <div class="expedited-passports-icons d-flex justify-content-center">
                <img width="220" height="30" src="./public/icons/nytimes.svg" alt="" srcset="">
                <img width="138" height="30" src="./public/icons/forbes.svg" alt="" srcset="">
                <img width="172" height="30" src="./public/icons/usa-today.svg" alt="" srcset="">
                <img width="130" height="30" src="./public/icons/msnb.svg" alt="" srcset="">
            </div>
        </section>
        <section class="four-steps">
            <div class="four-steps-content d-flex flex-column">
                <h1 class="fs-48 lh-58 text-center mb-0 four-step-head">4 steps. <span style="color: #FFC200;">Zero</span> stress. <br class="d-none d-md-block"/>
                   <span style="color: #FFC200;">One</span> approved passport.
                </h1>
                <div class="d-flex flex-wrap justify-content-center cards-group">
                    <div class="cards d-flex flex-column font-inter">
                        <img class="four-steps-content-image-new" height="178" src="./public/icons/visa.png" alt="" srcset="">
                        <span class="rounded-pill">Step 1</span>
                        <div class="d-flex flex-column gap-10">
                            <h1 class="mb-0">Apply Online in Minutes</h1>
                            <p class="mb-0 pr-1 lh-100">
                                Choose the passport or visa service <br/> you need. Apply for yourself, family, <br/> 
                                or friends with ease.
                            </p>
                        </div>
                        <div class="card-footer-text fw-medium lh-100">
                            <img width="12" height="12" src="./public/icons/checked-filled-green.svg" alt="" srcset="">
                            Simple & fast online form
                        </div>
                    </div>
                    <div class="cards d-flex flex-column font-inter">
                        <!-- <img height="178" src="./public/icons/passport-application.svg" alt="" srcset=""> -->
                        <img class="four-steps-content-image-new" height="178" src="./public/icons/passport-application.png" alt="" srcset="">
                        <span class="rounded-pill">Step 2</span>
                        <div class="d-flex flex-column gap-10">
                            <h1 class="mb-0">Complete Your Application </h1>
                            <p class="mb-0 pr-1 lh-100">
                                Use your Passports & Visas account to <br/> submit your documents and details online
                                . <br/> No long queues or confusing paperwork.
                            </p>
                        </div>
                        <div class="card-footer-text fw-medium lh-100">
                            <img width="12" height="12" src="./public/icons/checked-filled-green.svg" alt="" srcset="">
                            Secure & user-friendly process
                        </div>
                    </div>
                    <div class="cards d-flex flex-column font-inter">
                        <!-- <img height="178" src="./public/icons/passport-id.svg" alt="" srcset=""> -->
                        <img class="four-steps-content-image-new" height="178" src="./public/icons/passport-id.png" alt="" srcset="">
                        <span class="rounded-pill font-inter">Step 3</span>
                        <div class="d-flex flex-column gap-10">
                            <h1 class="mb-0">We Handle the Processing</h1>
                            <p class="mb-0 pr-1 lh-100">
                                Your application is reviewed, submitted, <br/> and tracked by your dedicated 
                                Account <br/> Manager—every step of the way.
                            </p>
                        </div>
                        <div class="card-footer-text fw-medium lh-100">
                            <img width="12" height="12" src="./public/icons/checked-filled-green.svg" alt="" srcset="">
                            Professionally reviewed & pre-filled
                        </div>
                    </div>
                    <div class="cards d-flex flex-column font-inter">
                        <!-- <img height="178" src="./public/icons/map.svg" alt="" srcset=""> -->
                        <img class="four-steps-content-image-new" height="178" src="./public/icons/map.png" alt="" srcset="">
                        <span class="rounded-pill font-inter">Step 4</span>
                        <div class="d-flex flex-column gap-10">
                            <h1 class="mb-0">Track Your Passport or Visa</h1>
                            <p class="mb-0 lh-100">
                                Stay informed with real-time updates via <br/> Email, SMS, and our dashboard.
                            </p>
                        </div>
                        <div class="card-footer-text fw-medium lh-100" style="vertical-align: bottom;">
                            <img width="12" height="12" src="./public/icons/checked-filled-green.svg" alt="" srcset="">
                            24/7 tracking access
                        </div>
                    </div>
                </div>
                <div class="four-steps-footer d-flex flex-column">
                    <h1 class="mb-0 text-center">
                         The Easiest Way to Apply for Your Passport, Guaranteed
                    </h1>
                    <div class="passport-renewal-get-started-btn">
                        <div class="dropdown">
                            <button class="passport-renewal-btn font-inter rounded-pill d-flex justify-content-between align-items-center" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                Passport Renewal 
                                <img src="./public/icons/arrow-outlined.svg" alt="" srcset="">
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                <li><a class="dropdown-item" href="#">Action</a></li>
                                <li><a class="dropdown-item" href="#">Another action</a></li>
                                <li><a class="dropdown-item" href="#">Something else here</a></li>
                            </ul>
                        </div>
                        <button class="get-started-your-app fw-medium rounded-pill d-inline-flex align-items-center" style="min-width: min-content;">
                            Get started Your Application 
                            <img class="get-started-your-app-img" src="./public/expedited/rounded-arrow.svg" alt="" srcset="">
                        </button>
                    </div>
                </div>
            </div>
        </section>
        <section class="what-people-are-saying d-flex flex-column">
            <div class="heading text-center d-flex flex-column">
                <h1 class="fs-48 lh-58 fw-bold mb-0 primary-blue tt-font">What <span class="primary-orange">People</span> are Saying</h1>
                <p class="mb-0 fs-14 lh-100 primary-blue font-inter">Verified Reviews from Real Customers</p>
            </div>
            <div class="d-flex gap-35">
                <div class="testimonial-section d-flex align-items-center">
                    <div class="image-carousel-new">
                        <div class="d-flex first-second-img-box">
                            <div class="testimonial-section-first-img rounded-pill">
                                <img width="100%" height="100%" src="./public/images/first-image.svg" />
                            </div>
                            <div class="testimonial-section-second-img rounded-pill">
                                <img width="100%" height="100%" src="./public/images/second-image.svg" />
                            </div>
                        </div>
                        <div class="third-four-img-box d-flex">
                            <div class="testimonial-section-third-img rounded-pill">
                                <img width="100%" height="100%" src="./public/images/third-image.svg" />
                            </div>
                            <div class="testimonial-section-four-img rounded-pill">
                                <img width="122" height="122" src="./public/images/four-image.svg" />
                            </div>
                        </div>
                        <div class="five-six-img-box d-flex">
                            <div class="testimonial-section-five-img rounded-pill">
                                <img width="100%" height="100%" src="./public/images/five-image.svg" />
                            </div>
                            <div class="testimonial-section-six-img rounded-pill">
                                <img width="100%" height="100%" src="./public/images/six-image.svg" />
                            </div>
                        </div>
                    </div>
                    <!-- Progress Dashes -->
                    <div class="progress-dashes">
                        <div class="dash active"></div>
                        <div class="dash"></div>
                        <div class="dash"></div>
                        <div class="dash"></div>
                        <div class="dash"></div>
                        <div class="dash"></div>
                    </div>

                    <!-- Testimonial Text -->
                    <div class="testimonial-content" id="testimonial-container"></div>
                </div>
            </div>
            <img class="what-people-are-saying-ellipse" src="./public/images/what-people-are-saying-ellipse.svg" alt="" srcset="">
            <img class="what-people-are-saying-ellipse-lg" src="./public/images/what-people-are-saying-ellipse-lg.svg" alt="" srcset="">
        </section>
        <section class="our-passport-vissa">
            <div class="our-passport-vissa-main">
                <div class="our-passport-vissa-main-heading d-flex flex-column text-center">
                    <h1 class="mb-0 fs-48 fw-bold lh-100 primary-blue">
                        Our <span class="primary-orange">Passport</span> & Visa Services
                    </h1>
                    <p class="">Quick solutions for urgent passport needs and seamless travel visas.</p>
                </div>
                <div class="our-passport-vissa-content d-flex">
                    <div class="text-carousel-container">
                        <div class="text-content" id="carousel-text"></div>
                        <div class="text-carousel-container-dashes">

                            <button class="get-started-your-app-text">Get Start Your Application</button>
                            <div class="carousel-dashes" id="carousel-dashes"></div>
                        </div>
                    </div>
                    <div class="cards">
                        <div class="card-content">
                            <h1 class="card-content-header-text primary-blue mb-3">What You will Get?</h1>
                            <div class="d-flex flex-column gap-12">
                                <div class="card-content-data d-flex gap-12 align-items-center">
                                    <div class="circle d-flex justify-content-center align-items-center rounded-circle">
                                        <img src="./public//icons/lock.svg" alt="" srcset="">
                                    </div>
                                    <p class="mb-0 card-content-data-sub-heading secondary-black">Secure</p>
                                
                                </div>
                                <div class="d-flex gap-12 align-items-center">
                                    <div class="circle-bulb d-flex justify-content-center align-items-center rounded-circle">
                                    <img src="./public/icons/bulb.svg" alt="" srcset="">
                                </div>
                                <p class="mb-0 card-content-data-sub-heading secondary-black">Avoid Mistakes</p>
                                
                                </div>
                                <div class="d-flex gap-12 align-items-center">
                                    <div class="circle-easy d-flex justify-content-center align-items-center rounded-circle">
                                        <img src="./public/icons/cube.svg" alt="" srcset="">
                                    </div>
                                    <p class="mb-0 card-content-data-sub-heading secondary-black">Easy</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="visa-options">
                    <div class="visa-options-cut-bg-header-div">
                        <img class="medium" src="./public/images/cut-BG.svg" alt="">
                        <img class="large" src="./public/images/cut-BG-lg.svg" alt="">
                    <div class="visa-options-top-div">
                        <img style="margin-right: 8px;" src="./public/icons/visa-optiion-top.svg" width="32" height="32" alt="">
                        Visas Options
                    </div>
                    <div>

                       <div class="visa-options-header">
                            <div class="prev-next">
                                <button class="prev">
                                    <img width="18" height="18" src="./public/icons/prev-btn.svg" alt="">
                                </button>
                                <button class="next">
                                    <img width="18" height="18" src="./public/icons/next-btn.svg" alt="">
                                </button>
                            </div>
                            <button class="view-all">
                                View All <img width="15" height="15" src="./public/images/arrow-right-blue.svg" alt="">
                            </button>
                       </div> 
                    </div>
                    </div>


                       <div class="visa-option-content">
                            <div class="visa-country">
                                <div class="visa-country-heading mb-0">
                                    <img width="27" height="20" src="./public/icons/china-flag.svg" alt="" srcset="">
                                    China Visas
                                </div>
                                <p class="subheading">China Visas are required for all US Citizens and many foreign nationals.</p>
                            </div>
                            <div class="visa-country">
                                <div class="visa-country-heading mb-0">
                                    <img width="27" height="20" src="./public/icons/india-flag.svg" alt="" srcset="">
                                    India Visas
                                </div>
                                <p class="subheading">China Visas are required for all US Citizens and many foreign nationals.</p>
                            </div>
                            <div class="visa-country">
                                <div class="visa-country-heading mb-0">
                                    <img width="27" height="20" src="./public/icons/russia-flag.svg" alt="" srcset="">
                                    Russia Visas
                                </div>
                                <p class="subheading">China Visas are required for all US Citizens and many foreign nationals.</p>
                            </div>
                            <div class="visa-country">
                                <div class="visa-country-heading mb-0">
                                    <img width="27" height="20" src="./public/icons/australia-flag.svg" alt="" srcset="">
                                    Australia Visas
                                </div>
                                <p class="subheading">China Visas are required for all US Citizens and many foreign nationals.</p>
                            </div>
                    </div>
                    <div class="visa-options-header-mobile">
                            <div class="prev-next">
                                <button class="prev">
                                    <img width="18" height="18" src="./public/icons/prev-btn.svg" alt="">
                                </button>
                                <button class="next">
                                    <img width="18" height="18" src="./public/icons/next-btn.svg" alt="">
                                </button>
                            </div>
                            <button class="view-all">
                                View All <img width="15" height="15" src="./public/images/arrow-right-blue.svg" alt="">
                            </button>
                       </div>
                </div>
            </div>
        </section>
        <section class="frequently-asked-questions">
            <div class="heading">
                <h1>Frequently Asked <br class="d-block d-md-none" /> Questions</h1>
                <p class="mb-0">We compiled a list of answers to address your most pressing <br class="d-none d-lg-block"/>
questions regarding our Services.</p>
            </div>
            <div class="content">
                <div class="list-item-btns">
                    <button class="tab-btn active" data-tab="new">New/First</button>
                    <button class="tab-btn" data-tab="renewal">Renewal</button>
                    <button class="tab-btn" data-tab="child">Child/Minor</button>
                    <button class="tab-btn" data-tab="lost">Lost/Stolen</button>
                    <button class="tab-btn" data-tab="damaged">Damaged</button>
                    <button class="tab-btn" data-tab="name">Name Change</button>
                </div>
                <div class="list-items-content">
                    <div class="tab-content active" data-tab="new">
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    What is a US Passport?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img style="cursor: pointer;" width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    I have never had a U.S. passport. How do I get one?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img style="cursor: pointer;" width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    I have never had a U.S. passport. How do I get one?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img style="cursor: pointer;" width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    I have never had a U.S. passport. How do I get one?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img style="cursor: pointer;" width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    My passport was lost / stolen. How do I get one?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img style="cursor: pointer;" width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                    </div>

                <div>
                    <div class="single-list-item">
                    <p class="title mb-0">
                        I have never had a U.S. passport. How do I get one?
                    </p>
                    <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                    </div>
                    <div class="single-list-item-open">
                    <div class="single-list-item-open-content">
                        <p class="mb-0 para">You’ll need to apply in person using Form DS-11.</p>
                        <p class="mb-0 para1">Bring original documents proving U.S. citizenship and a valid form of identification. You must also provide a passport photo and pay applicable fees.</p>
                    </div>
                    <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                    </div>
                </div>

                <div>
                    <div class="single-list-item">
                    <p class="title mb-0">
                        My passport was lost / stolen. How do I get one?
                    </p>
                    <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                    </div>
                    <div class="single-list-item-open">
                    <div class="single-list-item-open-content">
                        <p class="mb-0 para">Report the lost or stolen passport immediately by submitting Form DS-64.</p>
                        <p class="mb-0 para1">To get a new passport, submit Form DS-11 along with proof of identity, a new photo, and your travel plans if applying for expedited service.</p>
                    </div>
                    <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                    </div>
                </div>

                <div>
                    <div class="single-list-item">
                    <p class="title mb-0">
                        My child who is under 18 needs a passport. How do I get it?
                    </p>
                    <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                    </div>
                    <div class="single-list-item-open">
                    <div class="single-list-item-open-content">
                        <p class="mb-0 para">Children under 16 must apply in person with both parents or guardians using Form DS-11.</p>
                        <p class="mb-0 para1">Provide the child’s birth certificate, both parents’ ID, and consent documentation. Children aged 16-17 may apply with at least one parent present.</p>
                    </div>
                    <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                    </div>
                </div>
                </div>

            </div>
            <div class="list-items-footer">
                    <div class="left">
                        <h6>Still Have Questions?</h6>
                        <p>Please contact us, if you can't find the answer your looking for.</p>
                    </div>
                    <button class="contact-us">
                        Contact Us <img width="12" height="22" src="./public/icons/arrow-right-white.svg" alt="" srcset="">
                    </button>
                </div>
                <img class="right-side-plane-icon" src="./public/icons/plane-reverse.svg" alt="" srcset="">
        </section>
        <section class="see-our-blogs">
            <div class="main">
                <div class="heading">
                    <h1 class="mb-0">See Our <span class="primary-orange">Blogs</span></h1>
                    <button class="view-all-see">
                         View All
                    </button>
                </div>
                <div class="content">
                    <div class="see-our-blog-card">
                        <div class="see-our-blog-card-content">
                            <img width="340" height="210" src="./public/images/see-our-blog-img1.png" alt="">
                            <div class="see-our-blog-card-content-data">
                                <span class="passport-type passport-type-visa-not">Passport</span>
                                <h1>Do US Citizens Need A Visa For France Entry Requirements & 
                                    Application Process</h1>
                                <p>Will you need a vlga to enter France? Here are the entry 
                                    requirements and application</p>
                            </div>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                    <div class="see-our-blog-card">
                        <div class="see-our-blog-card-content">
                            <img width="340" height="210" src="./public/images/see-our-blog-img2.png" alt="">
                            <div class="see-our-blog-card-content-data">
                                <span class="passport-type">Visa</span>
                                <h1>Indian Business Visa for UK citizens</h1>
                                
                                <p>Will you need a vlga to enter France? Here are the entry 
                                    requirements and application</p>
                            </div>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                    <div class="see-our-blog-card">
                        <div class="see-our-blog-card-content">
                            <img width="340" height="210" src="./public/images/see-our-blog-img3.png" alt="">
                            <div class="see-our-blog-card-content-data">
                                <span class="passport-type passport-type-visa-not">Passport</span>
                                <h1>Do US Citizens Need A Visa For France Entry Requirements & Application Process</h1>
                                <p>Will you need a vlga to enter France? Here are the entry 
                                    requirements and application</p>
                            </div>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                </div>
                <button class="view-all-see-mobile">View All</button>
            </div>
        </section>
        <section class="footer-section">
            <div class="footer-section-main">
                <div class="footer-header-section">
                    <div class="logo-head">
                        <img width="112" height="70" src="./public/logo.svg" alt="" srcset="">
                        <div class="bottom-section-icon">
                            <img width="30" height="30" src="./public/icons/facebook.svg" alt="">
                            <img width="30" height="30" src="./public/icons/twitter.svg" alt="">
                        </div>
                    </div>
                    <p class="follow-for-more-header">Follow for More Updates</p>
                </div>
                <div class="left">
                    <div class="top-section">
                        <div class="content">
                            <h6>Passports</h6>
                            <div class="content-data">
                                <p>US Passports</p>
                                <p>Passport Renewal</p>
                                <p>New Passport</p>
                                <p>Child Passport</p>
                                <p>Second Passport</p>
                                <p>Name Change</p>
                                <p>Lost Passport</p>
                                <p>Local Passport Info</p>
                            </div>
                        </div>
                        <div class="content">
                            <h6>Travel Visas</h6>
                            <div class="content-data">
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/china.svg" width="21" height="16" alt="" srcset="">
                                     China Visa
                                    </p>
                                <p class="d-flex gap-10"> 
                                    <img src="./public/flags/india.svg" width="21" height="16" alt="" srcset="">
                                    India Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/russia.svg" width="21" height="16" alt="" srcset="">
                                    Russian Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/australia.svg" width="21" height="16" alt="" srcset="">
                                    Australia Visas
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/vietnam.svg" width="21" height="16" alt="" srcset="">
                                    Vietnam Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/saudia.svg" width="21" height="16" alt="" srcset="">
                                    Saudi Arabia Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/belgium.svg" width="21" height="16" alt="" srcset="">
                                    Belgium Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/indonesia.svg" width="21" height="16" alt="" srcset="">
                                    Indonesia Visa
                                </p>
                            </div>
                        </div>
                        <div class="content">
                            <h6>Company</h6>
                            <div class="content-data">
                                <p>About</p>
                                <p>Corporate Solutions</p>
                                <p>How It Works</p>
                                <p>Customers Reviews</p>
                                <p>Contact</p>
                            </div>
                        </div>
                        <div class="content">
                            <h6>Resources</h6>
                            <div class="content-data">
                                <p>Travel Blog</p>
                                <p>Recourse Center</p>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-section">
                        <div class="bottom-section-content">
                            <img onclick="location.href='index.html'" class="footer-logo" width="112" height="70" src="./public/logo.svg" alt="">
                            <div class="footer-social-media">
                                <p class="mb-0">Follow for More Updates</p>
                                <div class="bottom-section-icon">
                                    <img src="./public/icons/facebook.svg" alt="">
                                    <img src="./public/icons/twitter.svg" alt="">
                                </div>
                            </div>
                        </div>
                        <footer class="footer-contact">
                        © 2025 Passports and Visas. All rights reserved.
                        </footer>
                    </div>
                </div>
                <div class="right">
                    <div class="top">
                        <div class="top-header">
                            <span class="passport">
                                Passport
                            </span>
                            <p class="fast-track mb-0">Fast-Track Your Passport and Visas Today</p>
                            <button class="get-started-your-app-footer">
                                Get started Your Application
                                <img src="./public/icons/arrow-right-footer.svg" width="24" height="24" alt="" srcset="">
                            </button>
                        </div>
                        <div class="pre-footer-dot-indicator">
                            <div class="pre-footer-dot pre-footer-active"></div>
                            <div class="pre-footer-dot"></div>
                            <div class="pre-footer-dot"></div>
                        </div>
            <img class="footer-ellipse-img" src="./public/images/footer-ellipse.svg" alt="">

                    </div>
                </div>
            </div>
            <div class="footer-section-mobile-view">
            <footer class="footer-contact">
                © 2025 Passports and Visas. All rights reserved.
            </footer>
            </div>
        </section>
        
    </div>
    <div class="mobile-view-bottom-btns">
            <div class="content">
                <button class="mobile-view-btn">
                    <img width="24" height="24" src="./public/icons/mobile-home-icon.svg" alt="" srcset="">
                    Home
                </button>
                <button class="mobile-view-btn">
                    <img width="24" height="24" src="./public/icons/mobile-passport.svg" alt="" srcset="">
                    Passports
                </button>
                <button class="mobile-view-btn">
                    <img width="24" height="24" src="./public/icons/mobile-visa.svg" alt="" srcset="">
                    Visas
                </button>
                <button class="mobile-view-btn">
                    <img width="24" height="24" src="./public/icons/mobile-people.svg" alt="" srcset="">
                    Resources
                </button>
                <button class="mobile-view-btn">
                    <img width="24" height="24" src="./public/icons/mobile-more.svg" alt="" srcset="">
                    More
                </button>
            </div>
        </div>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const tabButtons = document.querySelectorAll('.list-item-btns button');
    const listItems = document.querySelectorAll('.list-items-content > div');

    // Activate the first tab by default
    if (tabButtons.length > 0) {
      tabButtons[0].classList.add('active');
      listItems.forEach((item, index) => {
        item.style.display = index === 0 ? 'block' : 'none';
      });
    }

    // Handle tab button clicks
    tabButtons.forEach((btn, index) => {
      btn.addEventListener('click', () => {
        // Remove active from all
        tabButtons.forEach(b => b.classList.remove('active'));
        listItems.forEach((item, i) => {
          item.style.display = i === index ? 'block' : 'none';

          // Close any open accordions in other tabs
          const openBlock = item.querySelector('.single-list-item-open');
          const heading = item.querySelector('.single-list-item');
          if (openBlock) openBlock.style.display = 'none';
          if (heading) heading.style.display = 'flex';
        });

        // Set active
        btn.classList.add('active');
      });
    });

    // Accordion open on plus
    document.querySelectorAll('.single-list-item-btn').forEach((icon) => {
      icon.addEventListener('click', () => {
        const parentItem = icon.closest('div').parentElement;

        // Close all others
        document.querySelectorAll('.single-list-item-open').forEach(el => el.style.display = 'none');
        document.querySelectorAll('.single-list-item').forEach(el => el.style.display = 'flex');

        // Show only this one
        const openContent = parentItem.querySelector('.single-list-item-open');
        const heading = parentItem.querySelector('.single-list-item');
        if (openContent) openContent.style.display = 'flex';
        if (heading) heading.style.display = 'none';
      });
    });

    // Accordion close on minus
    document.querySelectorAll('.single-list-item-open img').forEach((minusIcon) => {
      minusIcon.addEventListener('click', () => {
        const openContent = minusIcon.closest('.single-list-item-open');
        const parentItem = openContent.parentElement;
        const heading = parentItem.querySelector('.single-list-item');

        openContent.style.display = 'none';
        if (heading) heading.style.display = 'flex';
      });
    });

    // Initially hide all accordion contents
    document.querySelectorAll('.single-list-item-open').forEach(el => el.style.display = 'none');
  });
</script>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const targetImg = document.querySelector('.expedited-passports-img .start-your-app-img');

    if (targetImg) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            targetImg.classList.add('animate-float');
          }
        });
      }, { threshold: 0.5 });

      observer.observe(targetImg);
    }
  });
</script>
<script>
const testimonialData = [
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel a few hours before getting on
my next flight. Patty Henriquez made it happen for me. On top of
that She was kind to me through my entire chaotic request. Patty
is the best!`,
    rating: "Excellent",
    name: "Stephen Ferrera-Grand",
    verified: true
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel a few hours before getting on my next flight. Patty Henriquez made it happen for me.`,
    rating: "Excellent",
    name: "Ferrera-Grand",
    verified: true
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel. I had a complicated situation where I was moving from city to city. I received my passport at my hotel. I had a complicated situation.`,
    rating: "Excellent",
    name: "Stephen Ferrera-Grand",
    verified: false
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel a few hours before getting on my next flight. Patty Henriquez made it happen for me. On top of that she was kind to me through my entire chaotic request. Patty is the best!`,
    rating: "Excellent",
    name: "Stephen Ferrera-Grand",
    verified: true
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel a few hours before getting on my next flight. Patty Henriquez made it happen for me.`,
    rating: "Excellent",
    name: "Ferrera-Grand",
    verified: true
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel. I had a complicated situation where I was moving from city to city. I received my passport at my hotel. I had a complicated situation.`,
    rating: "Excellent",
    name: "Stephen Ferrera-Grand",
    verified: false
  }
];

const testimonialImages = [
  document.querySelector('.testimonial-section-first-img img'),
  document.querySelector('.testimonial-section-four-img img'),
  document.querySelector('.testimonial-section-five-img img')
];

const dashes = document.querySelectorAll(".dash");
const testimonialContainer = document.getElementById("testimonial-container");

let current = 0;

function renderTestimonial(index) {
  const t = testimonialData[index];

  // Update testimonial text
  testimonialContainer.innerHTML = `
    <div class="testimonial active d-flex flex-column justify-content-between">
      <p class="mb-0 testimonial-card-first-heading">
        ${t.text}
      </p>
      <div class="d-flex testimonial-section-main-content-head justify-content-between align-items-center">
        <div class="d-flex flex-column align-items-center testimonial-footer-section-left">
          <div class="d-flex flex-column gap-6 align-items-center">
            <span class="testimonial-section-top-content-heading fw-medium font-inter secondary-black-light lh-100 font-weight-medium">${t.rating}</span>
            <img class="stars-group-img-class" src="./public/icons/stars-group.svg" alt="Stars">
          </div>
          <img class="trust-img-class" src="./public/icons/trust-pilot.svg" alt="TrustPilot">
        </div>
        <div class="d-flex testimonial-footer-section-right flex-column lh-100 font-inter font-weight-medium">
          <p class="mb-0 fw-medium testimonial-section-bottom-content-heading primary-black">${t.name}</p>
          <p class="mb-0 fw-medium testimonial-section-bottom-content-para d-flex gap-2 d-flex justify-content-end" style="color: rgba(0, 0, 0, 0.8);">
            <img width="14" height="14" src="./public/icons/checked-filled-black.svg" alt="Verified Icon">
            ${t.verified ? "Verified" : "Not Verified"}
          </p>
        </div>
      </div>
    </div>
  `;

  // Animate image
  testimonialImages.forEach((img, i) => {
    img.classList.toggle("animate-img", i === index);
  });

  // Highlight active dash
  dashes.forEach((dash, i) => {
    dash.classList.toggle("active", i === index);
  });
}

renderTestimonial(current);

setInterval(() => {
  current = (current + 1) % testimonialData.length;
  renderTestimonial(current);
}, 12000);
</script>



<script>
const carouselData = [
  {
    heading: "Renew your Passport in Just 4 Days",
    description: "Save time and expedite your US passport renewal application in as little as 4 business days. Renew your expired passport fast!",
  },
  {
    heading: "Another Fast Government",
    description: "Get help with ID renewal, social security, and more — faster than ever.",
  },
  {
    heading: "Another Fast Service",
    description: "Get help with ID renewal, social security, and more — faster than ever.",
  },
  {
    heading: "Another Government Service",
    description: "Get help with ID renewal, social security, and more — faster than ever.",
  },
  {
    heading: "Another Fast Gover",
    description: "Get help with ID renewal, social security, and more — faster than ever.",
  }

];
const textContainer = document.getElementById("carousel-text");
const dashesContainer = document.getElementById("carousel-dashes");

function renderCarouselItem(index) {
  const item = carouselData[index];

  textContainer.innerHTML = `
    <h4 class="carousel-heading">${item.heading}</h4>
    <p class="carousel-description">${item.description}</p>
  `;

  const dashHTML = carouselData
    .map((_, i) => `<span class="our-passport-dash ${i === index ? "active" : ""}"></span>`)
    .join("");
  dashesContainer.innerHTML = dashHTML;
}

let currentActiveDash = 0;
renderCarouselItem(currentActiveDash);

setInterval(() => {
  currentActiveDash = (currentActiveDash + 1) % carouselData.length;
  renderCarouselItem(currentActiveDash);
}, 12000);

</script>
<script>
  const dots = document.querySelectorAll('.pre-footer-dot');
  let currentDot = 0;

  setInterval(() => {
    // Remove active from all
    dots.forEach(dot => dot.classList.remove('pre-footer-active'));

    // Add active to current
    currentDot = (currentDot + 1) % dots.length;
    dots[currentDot].classList.add('pre-footer-active');
  }, 2000); // change every 2 seconds
</script>
<script>
  const items = document.querySelectorAll('.high-approval-rate');
  let currentApprovalRate = 0;

  setInterval(() => {
    items[currentApprovalRate].classList.remove('active');
    currentApprovalRate = (currentApprovalRate + 1) % items.length;
    items[currentApprovalRate].classList.add('active');
  }, 113000);
</script>

</body>
</html>