<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="./index.css">
    <link rel="stylesheet" href="./styles.css">
    <link rel="stylesheet" href="./get-passport-in-three-days.css">
    <link rel="stylesheet" href="./choose-the-passport-service.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="stylesheet" href="./what-people-are.css">
    <link rel="stylesheet" href="./get-your-new-passport.css">
    <link rel="stylesheet" href="./frequently-asked-questions.css">
    <link rel="stylesheet" href="./see-our-blogs.css">
    <link rel="stylesheet" href="./footer-section.css">
    <link href='https://fonts.googleapis.com/css?family=Inter' rel='stylesheet'>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
</head>
<body>
    <div class="header-class-top-sec">
        <div class="row no-gutters align-items-center container-fluid pl-0 justify-content-center text-white fw-medium fs-14 header-class">
            <div class="col-12 col-md-4 text-white p-0"></div>
            <div class="col-12 col-md-4 p-0">
                <p class="mb-0 text-end d-flex justify-content-end gap-2" style="font-family: 'Inter', sans-serif;">
                    <img width="18" height="18" class="bell-icon-class" src="./public/icons/bell.svg" alt="" srcset="">
                    Get Your Passport in Just 24 Hours – Now Available!
                </p>
            </div>
            <div class="col-12 col-md-4 p-0">
                <div class="d-flex gap-4 justify-content-end">
                    <div class="d-flex justify-content-center gap-2">
                        <img src="./public/phone.svg" alt="" width="16" height="16">
                        <span class="font-inter font-medium" style="font-weight: 500;">Phone: (************)</span>
                    </div>
                    <div class="d-flex justify-content-center gap-2">
                        <img src="./public/mdi_location.svg" alt="">
                        <span class="font-inter" style="font-weight: 500;">Track My Application</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid p-0">
        

        <nav class="navbar navbar-light">
            <div class="container-fluid p-0 nav-bar-content">
                <img style="cursor: pointer;" onclick="location.href='index.html'" width="112" height="70" src="./public/logo.svg" alt="" srcset="">
                <div class="d-flex nav-btns">
                    <div class="pre-custom-dropdown">
                        <button class="pre-custom-btn pre-custom-dropdown-toggle pre-custom-dropdown-button" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Passports
                                <img width="10" height="10" src="./public/icons/arrow-down-black.svg" alt="" srcset="">
                            </button>
                        </button>
                        <ul class="pre-custom-dropdown-menu dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Another action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Something else</a></li>
                        </ul>
                    </div>
                    <div class="pre-custom-dropdown">
                        <button class="pre-custom-btn pre-custom-dropdown-toggle pre-custom-dropdown-button" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Travel visas
                                <img width="10" height="10" src="./public/icons/arrow-down-black.svg" alt="" srcset="">
                            </button>
                        </button>
                        <ul class="pre-custom-dropdown-menu dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Another action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Something else</a></li>
                        </ul>
                    </div>
                    <div class="pre-custom-dropdown">
                        <button class="pre-custom-btn pre-custom-dropdown-toggle pre-custom-dropdown-button" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            About
                                <img width="10" height="10" src="./public/icons/arrow-down-black.svg" alt="" srcset="">
                            </button>
                        </button>
                        <ul class="pre-custom-dropdown-menu dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Another action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Something else</a></li>
                        </ul>
                    </div>
                    <div class="pre-custom-dropdown">
                        <button class="pre-custom-btn pre-custom-dropdown-toggle pre-custom-dropdown-button" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Resourses
                                <img width="10" height="10" src="./public/icons/arrow-down-black.svg" alt="" srcset="">
                            </button>
                        </button>
                        <ul class="pre-custom-dropdown-menu dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Another action</a></li>
                            <li><a class="pre-custom-dropdown-item dropdown-item" href="#">Something else</a></li>
                        </ul>
                    </div>
                    <button class="pre-custom-btn pre-custom-dropdown-button">
                        Contact
                    </button>
                    <button class="btn login-btn" style="margin-left: 20px;" type="submit">Log in</button>
                </div>
            </div> 
        </nav>
        <section class="get-passport-in-three-days">
            <div class="top-sec">
                <div class="left">
                    <div class="left-section">
                        <div class="get-your-passport-info-tag">
                            <div class="get-your-info-tag">
                            <span>From $199+</span> <span class="info-tag-span">Government fee</span>
                            </div>
                            <div class="get-your-info-tag">
                            <span>3–4 Business Days</span> <span class="info-tag-span">with Fast Delivery</span>
                            </div>
                        </div>
                        <h1 class="left-section-heading">
                            Get Your Passport in <span>Just 3</span> Days
                        </h1>
                        <p class="left-section-para">
                            Our visa consultant expedites your application by liaising directly with foreign
                             consulates for swift and secure processing (*Excludes Consular Fees).
                        </p>
                        <button class="get-started-your-app fw-medium rounded-pill d-inline-flex align-items-center" style="min-width: min-content;min-height: 52px;">
                            Started Your Application 
                            <img class="get-started-your-app-img" src="./public/expedited/rounded-arrow.svg" alt="" srcset="">
                        </button>
                        <div class="left-section-icons">
                            <div class="left-section-icons-content">
                                <img width="15" height="15" src="./public/icons/charm-checked-white.svg" alt="">
                                Secure
                            </div>
                            <div class="left-section-icons-content">
                                <img width="15" height="15" src="./public/icons/charm-checked-white.svg" alt="">
                                Avoid Mistakes
                            </div>
                            <div class="left-section-icons-content">
                                <img width="15" height="15" src="./public/icons/charm-checked-white.svg" alt="">
                                Easy
                            </div>
                        </div>
                        <div class="d-flex align-items-center text-black get-passport-in-reviews">
                            <span style="font-family: 'Inter', sans-serif;" class="lh-100">11,484 reviews</span>
                            <img width="80" height="14" src="./public/icons/stars-group.svg" alt="">
                            <img width="70" height="17" src="./public/icons/trust-pilot.svg" alt="" srcset="">
                        </div>
                    </div>
                </div>
                <div class="right">
                    <img class="right-image" width="660" height="580" src="./public/images/passport-bg.svg" alt="" srcset="">
                    <img class="right-image-lg" width="660" height="580" src="./public/images/passport-bg-lg.png" alt="" srcset="">
                </div>

            </div>
            <div class="expedited-passports-icons d-flex justify-content-center">
                <img width="220" height="30" src="./public/icons/nytimes.svg" alt="" srcset="">
                <img width="138" height="30" src="./public/icons/forbes.svg" alt="" srcset="">
                <img width="172" height="30" src="./public/icons/usa-today.svg" alt="" srcset="">
                <img width="130" height="30" src="./public/icons/msnb.svg" alt="" srcset="">
            </div>
            <img class="left-side-plane-icon-get-passport-in" src="./public/icons/plane.svg" alt="">
        </section>
        <section class="choose-the-passport-service">
            <div class="choose-the-passport-service-main">
                <h1 class="mb-0">Choose the Passport Service</h1>
                <div class="middle-content">
                    <button class="middle-content-btn">
                        Passport Renewal
                    </button>
                    <button class="middle-content-btn">
                        New Renewal
                    </button>
                    <button class="middle-content-btn">
                        Child Renewal
                    </button>
                    <button class="middle-content-btn">
                        Second Renewal
                    </button>
                    <button class="middle-content-btn">
                        Name Change
                    </button>
                    <button class="middle-content-btn">
                        Lost Passport
                    </button>
                </div>
                <div class="end-content">
                    <div class="left">
                        <div class="top">
                            <div class="top-content">
                                <h1 class="mb-0">Renew Your Passport in Minutes.</h1>
                                <p class="mb-0">Complete your passport renewal application in under
                                     10 minutes and get it checked to make sure it’s approved first time.</p>
                            </div>
                        </div>
                        <div class="bottom">
                            <div class="bottom-content">
                                <div class="bottom-content-img">
                                    <img width="17" height="17" src="./public/icons/secured.svg" alt="" srcset="">
                                </div>
                                Secure Payments
                            </div>
                            <div class="bottom-content">
                                <div class="bottom-content-img">
                                    <img width="17" height="17" src="./public/icons/shipping-fast.svg" alt="" srcset="">
                                </div>
                                Free & Free Shipping
                            </div>
                            <div class="bottom-content">
                                <div class="bottom-content-img">
                                    <img width="17" height="17" src="./public/icons/user-name.svg" alt="" srcset="">
                                </div>
                                Personal Account Manager
                            </div>
                        </div>
                        <button class="get-started-btn">
                            Get Started
                        </button>
                    </div>
                    <div class="right">
                        <button class="right-btn active">
                            <div class="right-btn-main">
                                <img class="icon checked" src="./public/icons/checked-blue.svg" alt="">
                                <img class="icon unchecked" src="./public/icons/unchecked-blue.svg" alt="">

                                <div class="right-btn-content">
                                    <h6 class="active">Standard Expedited</h6>
                                    <p class="active">15 Business days</p>
                                </div>
                                <span class="discount active">
                                    Save $79.99
                                </span>
                            </div>
                            <div class="amount active">
                                $199
                            </div>
                        </button>
                        <button class="right-btn">
                            <div class="right-btn-main">
                                <img class="icon checked" src="./public/icons/checked-blue.svg" alt="">
                                <img class="icon unchecked" src="./public/icons/unchecked-blue.svg" alt="">

                                <div class="right-btn-content">
                                    <h6>Priority Expedited</h6>
                                    <p>10 Business days</p>
                                </div>
                                <span class="discount">
                                    Save $79.99
                                </span>
                            </div>
                            <div class="amount">
                                $299
                            </div>
                        </button>
                        <button class="right-btn">
                            <div class="right-btn-main">
                                <img class="icon checked" src="./public/icons/checked-blue.svg" alt="">
                                <img class="icon unchecked" src="./public/icons/unchecked-blue.svg" alt="">
                                <div class="right-btn-content">
                                    <h6>Emergency Expedited</h6>
                                    <p>4 Business days</p>
                                </div>
                                <span class="discount">
                                    Save $79.99
                                </span>
                            </div>
                            <div class="amount">
                                $399
                            </div>
                        </button>
                        <button class="right-btn">
                            <div class="right-btn-main">
                                <img class="icon checked" src="./public/icons/checked-blue.svg" alt="">
                                <img class="icon unchecked" src="./public/icons/unchecked-blue.svg" alt="">
                                <div class="right-btn-content">
                                    <h6>Standard Expedited</h6>
                                    <p>15 Business days</p>
                                </div>
                                <span class="discount">
                                    Save $79.99
                                </span>
                            </div>
                            <div class="amount">
                                $499
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </section>
        <section class="what-people-are-saying d-flex flex-column gap-5">
            <div class="heading text-center d-flex flex-column">
                <h1 class="fs-48 lh-58 fw-bold mb-0 primary-blue tt-font">What <span class="primary-orange">People</span> are Saying</h1>
                <p class="mb-0 fs-14 lh-100 primary-blue font-inter">Verified Reviews from Real Customers</p>
            </div>
            <div class="d-flex gap-35">
                <div class="testimonial-section d-flex align-items-center">
                    <div class="image-carousel-new">
                        <div class="d-flex first-second-img-box">
                            <div class="testimonial-section-first-img rounded-pill">
                                <img width="102" height="102" src="./public/images/first-image.svg" />
                            </div>
                            <div class="testimonial-section-second-img rounded-pill">
                                <img width="92" height="92" src="./public/images/second-image.svg" />
                            </div>
                        </div>
                        <div class="third-four-img-box d-flex">
                            <div class="testimonial-section-third-img rounded-pill">
                                <img width="60" height="60" src="./public/images/third-image.svg" />
                            </div>
                            <div class="testimonial-section-four-img rounded-pill">
                                <img width="122" height="122" src="./public/images/four-image.svg" />
                            </div>
                        </div>
                        <div class="five-six-img-box d-flex">
                            <div class="testimonial-section-five-img rounded-pill">
                                <img width="108" height="108" src="./public/images/five-image.svg" />
                            </div>
                            <div class="testimonial-section-six-img rounded-pill">
                                <img width="100" height="100" src="./public/images/six-image.svg" />
                            </div>
                        </div>
                    </div>
                    <!-- Progress Dashes -->
                    <div class="progress-dashes">
                        <div class="dash active"></div>
                        <div class="dash"></div>
                        <div class="dash"></div>
                        <div class="dash"></div>
                        <div class="dash"></div>
                        <div class="dash"></div>
                    </div>

                    <!-- Testimonial Text -->
                    <div class="testimonial-content" id="testimonial-container"></div>
                </div>
            </div>
            <!-- <img class="what-people-are-saying-ellipse" src="./public/images/what-people-are-saying-ellipse.svg" alt="" srcset=""> -->
        </section>
        <section class="get-your-new-passport">
            <div class="get-your-new-passport-main">
                <div class="heading">
                    <h1>Get your new passport <br/> in three simple steps</h1>
                    <button style="background-color: rgba(11, 65, 125, 1);;" class="get-started-your-app fw-medium rounded-pill d-inline-flex align-items-center" style="min-width: min-content;">
                        Get started Your Application 
                        <img class="get-started-your-app-img" src="./public/expedited/rounded-arrow.svg" alt="" srcset="">
                    </button>
                </div>
                <div class="get-your-new-passport-cards-card-group">
                    <div class="get-your-new-passport-cards d-flex flex-column font-inter">
                        <img height="237" width="300" src="./public/icons/passport-application.svg" alt="" srcset="">
                        <span class="">Step 1</span>
                        <div class="d-flex flex-column gap-13">
                            <h1 class="mb-0">Complete Your Application </h1>
                            <p class="mb-0 pr-1 lh-100">
                                Use your Passports & Visas account to <br/> submit your documents and details online. 
                                <br/> No long queues or confusing paperwork.
                            </p>
                        </div>
                    </div>
                    <div class="get-your-new-passport-cards d-flex flex-column font-inter">
                        <img height="237" width="300" src="./public/icons/passport-id.svg" alt="" srcset="">
                        <span class="">Step 2</span>
                        <div class="d-flex flex-column gap-13">
                            <h1 class="mb-0">Scan & Verify Your Documents</h1>
                            <p class="mb-0 pr-1 lh-100">
                                Scan your documents from any device — we’ll check 
                                them instantly for a smooth application.
                            </p>
                        </div>
                    </div>
                    <div class="get-your-new-passport-cards d-flex flex-column font-inter">
                        <img height="237" width="300" src="./public/icons/map.svg" alt="" srcset="">
                        <span class="">Step 3</span>
                        <div class="d-flex flex-column gap-13">
                            <h1 class="mb-0">Track Your Passports</h1>
                            <p class="mb-0 pr-1 lh-100">
                                Stay informed with real-time updates via Email, SMS, and our dashboard.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <img class="get-your-new-passport-ellipse" src="./public/images/what-people-are-saying-ellipse.svg" alt="" srcset="">
        </section>
        <section class="frequently-asked-questions">
            <div class="heading">
                <h1>Frequently Asked Questions</h1>
                <p class="mb-0">We compiled a list of answers to address your most pressing <br class="d-none d-lg-block"/>
questions regarding our Services.</p>
            </div>
            <div class="content">
                <div class="list-item-btns">
                    <button class="tab-btn active" data-tab="new">New/First</button>
                    <button class="tab-btn" data-tab="renewal">Renewal</button>
                    <button class="tab-btn" data-tab="child">Child/Minor</button>
                    <button class="tab-btn" data-tab="lost">Lost/Stolen</button>
                    <button class="tab-btn" data-tab="damaged">Damaged</button>
                    <button class="tab-btn" data-tab="name">Name Change</button>
                </div>
                <div class="list-items-content">
                    <div class="tab-content active" data-tab="new">
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    What is a US Passport?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    I have never had a U.S. passport. How do I get one?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                        
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    My passport was lost / stolen. How do I get one?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                        <div>
                            <div class="single-list-item">
                                <p class="title mb-0" style="font-weight: 500;">
                                    My child who is under 18 needs a passport. How do I get it?
                                </p>
                                <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                            </div>
                            <div class="single-list-item-open">
                                <div class="single-list-item-open-content">
                                    <h6 class="mb-0 para">I am traveling very soon. How do I get a passport in a hurry?</h6>
                                    <p class="mb-0 para1">You may download the passport forms from this website.
                                        Forms are also available at passport acceptance facilities like Post Offices, courthouses, or municipal buildings.
                                    </p>
                                </div>
                                <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                            </div>
                        </div>
                    </div>

                <div>
                    <div class="single-list-item">
                    <p class="title mb-0">
                        I have never had a U.S. passport. How do I get one?
                    </p>
                    <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                    </div>
                    <div class="single-list-item-open">
                    <div class="single-list-item-open-content">
                        <p class="mb-0 para">You’ll need to apply in person using Form DS-11.</p>
                        <p class="mb-0 para1">Bring original documents proving U.S. citizenship and a valid form of identification. You must also provide a passport photo and pay applicable fees.</p>
                    </div>
                    <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                    </div>
                </div>

                <div>
                    <div class="single-list-item">
                    <p class="title mb-0">
                        My passport was lost / stolen. How do I get one?
                    </p>
                    <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                    </div>
                    <div class="single-list-item-open">
                    <div class="single-list-item-open-content">
                        <p class="mb-0 para">Report the lost or stolen passport immediately by submitting Form DS-64.</p>
                        <p class="mb-0 para1">To get a new passport, submit Form DS-11 along with proof of identity, a new photo, and your travel plans if applying for expedited service.</p>
                    </div>
                    <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                    </div>
                </div>

                <div>
                    <div class="single-list-item">
                    <p class="title mb-0">
                        My child who is under 18 needs a passport. How do I get it?
                    </p>
                    <img class="single-list-item-btn" src="./public/icons/plus-blue.svg" alt="" srcset="">
                    </div>
                    <div class="single-list-item-open">
                    <div class="single-list-item-open-content">
                        <p class="mb-0 para">Children under 16 must apply in person with both parents or guardians using Form DS-11.</p>
                        <p class="mb-0 para1">Provide the child’s birth certificate, both parents’ ID, and consent documentation. Children aged 16-17 may apply with at least one parent present.</p>
                    </div>
                    <img width="32" height="32" class="" src="./public/icons/minus-blue.svg" alt="" srcset="">
                    </div>
                </div>
                </div>

            </div>
            <div class="list-items-footer">
                <div class="left">
                    <h6>Still Have Questions?</h6>
                    <p>Please contact us, if you can't find the answer your looking for.</p>
                </div>
                <button class="contact-us">
                    Contact Us <img width="12" height="22" src="./public/icons/arrow-right-white.svg" alt="" srcset="">
                </button>
            </div>
            <img class="right-side-plane-icon" src="./public/icons/plane-reverse.svg" alt="" srcset="">
        </section>
        <section class="see-our-blogs">
            <div class="main">
                <div class="heading">
                    <h1 class="mb-0">See Our <span class="primary-orange">Blogs</span></h1>
                    <button class="view-all-see">
                         View All
                    </button>
                </div>
                <div class="content">
                    <div class="see-our-blog-card">
                        <div class="see-our-blog-card-content">
                            <img width="340" height="210" src="./public/images/see-our-blog-img1.svg" alt="">
                            <div class="see-our-blog-card-content-data">
                                <span class="passport-type passport-type-visa-not">Passport</span>
                                <h1>Do US Citizens Need A Visa For France Entry Requirements & 
                                    Application Process</h1>
                                <p>Will you need a vlga to enter France? Here are the entry 
                                    requirements and application</p>
                            </div>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                    <div class="see-our-blog-card">
                        <div class="see-our-blog-card-content">
                            <img width="340" height="210" src="./public/images/see-our-blog-img2.svg" alt="">
                            <div class="see-our-blog-card-content-data">
                                <span class="passport-type">Visa</span>
                                <h1>Indian Business Visa for UK citizens</h1>
                                
                                <p>Will you need a vlga to enter France? Here are the entry 
                                    requirements and application</p>
                            </div>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                    <div class="see-our-blog-card">
                        <div class="see-our-blog-card-content">
                            <img width="340" height="210" src="./public/images/see-our-blog-img3.svg" alt="">
                            <div class="see-our-blog-card-content-data">
                                <span class="passport-type passport-type-visa-not">Passport</span>
                                <h1>Do US Citizens Need A Visa For France Entry Requirements & Application Process</h1>
                                <p>Will you need a vlga to enter France? Here are the entry 
                                    requirements and application</p>
                            </div>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                </div>
            </div>
        </section>
        <section class="footer-section">
            <div class="footer-section-main">
                <div class="left">
                    <div class="top-section">
                        <div class="content">
                            <h6>Passports</h6>
                            <div class="content-data">
                                <p>US Passports</p>
                                <p>Passport Renewal</p>
                                <p>New Passport</p>
                                <p>Child Passport</p>
                                <p>Second Passport</p>
                                <p>Name Change</p>
                                <p>Lost Passport</p>
                                <p>Local Passport Info</p>
                            </div>
                        </div>
                        <div class="content">
                            <h6>Passports</h6>
                            <div class="content-data">
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/china.svg" width="21" height="16" alt="" srcset="">
                                     China Visa
                                    </p>
                                <p class="d-flex gap-10"> 
                                    <img src="./public/flags/india.svg" width="21" height="16" alt="" srcset="">
                                    India Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/russia.svg" width="21" height="16" alt="" srcset="">
                                    Russian Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/australia.svg" width="21" height="16" alt="" srcset="">
                                    Australia Visas
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/vietnam.svg" width="21" height="16" alt="" srcset="">
                                    Vietnam Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/saudia.svg" width="21" height="16" alt="" srcset="">
                                    Saudi Arabia Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/belgium.svg" width="21" height="16" alt="" srcset="">
                                    Belgium Visa
                                </p>
                                <p class="d-flex gap-10">
                                    <img src="./public/flags/indonesia.svg" width="21" height="16" alt="" srcset="">
                                    Indonesia Visa
                                </p>
                            </div>
                        </div>
                        <div class="content">
                            <h6>Passports</h6>
                            <div class="content-data">
                                <p>About</p>
                                <p>Corporate Solutions</p>
                                <p>How It Works</p>
                                <p>Customers Reviews</p>
                                <p>Contact</p>
                            </div>
                        </div>
                        <div class="content">
                            <h6>Passports</h6>
                            <div class="content-data">
                                <p>Travel Blog</p>
                                <p>Recourse Center</p>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-section">
                        <div class="bottom-section-content">
                            <img onclick="location.href='index.html'" class="footer-logo" width="112" height="70" src="./public/logo.svg" alt="">
                            <div class="footer-social-media">
                                <p class="mb-0">Follow for More Updates</p>
                                <div class="bottom-section-icon">
                                    <img src="./public/icons/facebook.svg" alt="">
                                    <img src="./public/icons/twitter.svg" alt="">
                                </div>
                            </div>
                        </div>
                        <footer class="footer-contact">
                        © 2025 Passports and Visas. All rights reserved.
                        </footer>
                    </div>
                </div>
                <div class="right">
                    <div class="top">
                        <div class="top-header">

                            <span class="passport">
                                Passport
                            </span>
                            <p class="fast-track mb-0">Fast-Track Your Passport and Visas Today</p>
                            <button class="get-started-your-app-footer">
                                Get started Your Application
                                <img src="./public/icons/arrow-right-footer.svg" width="24" height="24" alt="" srcset="">
                            </button>
                        </div>
                        <div class="pre-footer-dot-indicator">
                            <div class="pre-footer-dot pre-footer-active"></div>
                            <div class="pre-footer-dot"></div>
                            <div class="pre-footer-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const tabButtons = document.querySelectorAll('.list-item-btns button');
    const listItems = document.querySelectorAll('.list-items-content > div');

    // Activate the first tab by default
    if (tabButtons.length > 0) {
      tabButtons[0].classList.add('active');
      listItems.forEach((item, index) => {
        item.style.display = index === 0 ? 'block' : 'none';
      });
    }

    // Handle tab button clicks
    tabButtons.forEach((btn, index) => {
      btn.addEventListener('click', () => {
        // Remove active from all
        tabButtons.forEach(b => b.classList.remove('active'));
        listItems.forEach((item, i) => {
          item.style.display = i === index ? 'block' : 'none';

          // Close any open accordions in other tabs
          const openBlock = item.querySelector('.single-list-item-open');
          const heading = item.querySelector('.single-list-item');
          if (openBlock) openBlock.style.display = 'none';
          if (heading) heading.style.display = 'flex';
        });

        // Set active
        btn.classList.add('active');
      });
    });

    // Accordion open on plus
    document.querySelectorAll('.single-list-item-btn').forEach((icon) => {
      icon.addEventListener('click', () => {
        const parentItem = icon.closest('div').parentElement;

        // Close all others
        document.querySelectorAll('.single-list-item-open').forEach(el => el.style.display = 'none');
        document.querySelectorAll('.single-list-item').forEach(el => el.style.display = 'flex');

        // Show only this one
        const openContent = parentItem.querySelector('.single-list-item-open');
        const heading = parentItem.querySelector('.single-list-item');
        if (openContent) openContent.style.display = 'flex';
        if (heading) heading.style.display = 'none';
      });
    });

    // Accordion close on minus
    document.querySelectorAll('.single-list-item-open img').forEach((minusIcon) => {
      minusIcon.addEventListener('click', () => {
        const openContent = minusIcon.closest('.single-list-item-open');
        const parentItem = openContent.parentElement;
        const heading = parentItem.querySelector('.single-list-item');

        openContent.style.display = 'none';
        if (heading) heading.style.display = 'flex';
      });
    });

    // Initially hide all accordion contents
    document.querySelectorAll('.single-list-item-open').forEach(el => el.style.display = 'none');
  });
</script>
<script>
const testimonialData = [
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel a few hours before getting on
my next flight. Patty Henriquez made it happen for me. On top of
that She was kind to me through my entire chaotic request. Patty
is the best!`,
    rating: "Excellent",
    name: "Stephen Ferrera-Grand",
    verified: true
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel a few hours before getting on my next flight. Patty Henriquez made it happen for me.`,
    rating: "Excellent",
    name: "Ferrera-Grand",
    verified: true
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel. I had a complicated situation where I was moving from city to city. I received my passport at my hotel. I had a complicated situation.`,
    rating: "Excellent",
    name: "Stephen Ferrera-Grand",
    verified: false
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel a few hours before getting on my next flight. Patty Henriquez made it happen for me. On top of that she was kind to me through my entire chaotic request. Patty is the best!`,
    rating: "Excellent",
    name: "Stephen Ferrera-Grand",
    verified: true
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel a few hours before getting on my next flight. Patty Henriquez made it happen for me.`,
    rating: "Excellent",
    name: "Ferrera-Grand",
    verified: true
  },
  {
    text: `I had a complicated situation where I was moving from city to city. I received my passport at my hotel. I had a complicated situation where I was moving from city to city. I received my passport at my hotel. I had a complicated situation.`,
    rating: "Excellent",
    name: "Stephen Ferrera-Grand",
    verified: false
  }
];

const testimonialImages = [
  document.querySelector('.testimonial-section-first-img img'),
  document.querySelector('.testimonial-section-four-img img'),
  document.querySelector('.testimonial-section-five-img img')
];

const dashes = document.querySelectorAll(".dash");
const testimonialContainer = document.getElementById("testimonial-container");

let current = 0;

function renderTestimonial(index) {
  const t = testimonialData[index];

  // Update testimonial text
  testimonialContainer.innerHTML = `
    <div class="testimonial active d-flex flex-column justify-content-between">
      <p class="mb-0 testimonial-card-first-heading">
        ${t.text}
      </p>
      <div class="d-flex justify-content-between align-items-center pt-4">
        <div class="d-flex flex-column align-items-center gap-3">
          <div class="d-flex flex-column gap-6 align-items-center">
            <span class="fs-18 fw-medium font-inter secondary-black-light lh-100 font-weight-medium">${t.rating}</span>
            <img height="22" src="./public/icons/stars-group.svg" alt="Stars">
          </div>
          <img height="23" src="./public/icons/trust-pilot.svg" alt="TrustPilot">
        </div>
        <div class="d-flex flex-column gap-4 h-64 lh-100 font-inter font-weight-medium">
          <p class="mb-0 fw-medium fs-5 primary-black">${t.name}</p>
          <p class="mb-0 fs-5 fw-medium d-flex gap-2 d-flex justify-content-end" style="color: rgba(0, 0, 0, 0.8);">
            <img width="14" height="14" src="./public/icons/checked-filled-black.svg" alt="Verified Icon">
            ${t.verified ? "Verified" : "Not Verified"}
          </p>
        </div>
      </div>
    </div>
  `;

  // Animate image
  testimonialImages.forEach((img, i) => {
    img.classList.toggle("animate-img", i === index);
  });

  // Highlight active dash
  dashes.forEach((dash, i) => {
    dash.classList.toggle("active", i === index);
  });
}

renderTestimonial(current);

setInterval(() => {
  current = (current + 1) % testimonialData.length;
  renderTestimonial(current);
}, 2000);
</script>    
</body>
</html>